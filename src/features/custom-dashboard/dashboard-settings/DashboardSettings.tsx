import { InsightDashboard, InsightDashboardFilters } from '@g17eco/types/insight-custom-dashboard';
import { SwitchGroup } from '@g17eco/molecules/switch-group';
import { SimpleTooltip } from '@g17eco/molecules/simple-tooltip';

export interface DashboardSettingsProps {
  filtersInput: InsightDashboard['filters'];
  handleToggle: (key: string) => (e: React.ChangeEvent<HTMLInputElement>) => void;
}

export const DefaultInsightViewSwitcher = ({ filtersInput, handleToggle }: DashboardSettingsProps) => {
  console.log(filtersInput);
  
  return (
    <SimpleTooltip
      text={
        'Enabling this toggle will set this dashboard as the default view for the insights page for each portfolio that you set up.'
      }
    >
      <SwitchGroup
        filterKey={InsightDashboardFilters.DisplayAsDefault}
        checked={filtersInput.displayAsDefault?.enabled}
        label='Set default insights view'
        handleToggle={handleToggle}
      />
    </SimpleTooltip>
  );
};
