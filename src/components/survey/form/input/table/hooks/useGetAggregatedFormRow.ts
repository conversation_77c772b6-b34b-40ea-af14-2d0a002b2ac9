import { useLazyGetAggregatedTableDataQuery } from '@api/utrv';
import { InputColumn } from '@components/survey/form/input/table/InputInterface';
import { convertInputData } from '@utils/valueDataTable';
import { TableDataInfo } from '@components/survey/question/questionInterfaces';
import { InitiativeUniversalTracker } from '@g17eco/types/initiativeUniversalTracker';
import { UniversalTrackerPlain } from '@g17eco/types/universalTracker';
import { useSiteAlert } from '@hooks/useSiteAlert';
import { isTableGroupAggregation } from '@utils/universalTracker';
import { useEffect, useMemo, useState } from 'react';
import { useDebouncedCallback } from 'use-debounce';

interface Props {
  utr: Pick<UniversalTrackerPlain, 'valueValidation'> | undefined;
  utrvId: string | undefined;
  table: TableDataInfo;
  initiativeUtr?: Pick<InitiativeUniversalTracker, 'valueValidation'>;
  formRow?: InputColumn[];
  editRowId?: number;
}

export const useGetAggregatedFormRow = ({ utr, table, formRow, editRowId, initiativeUtr, utrvId }: Props) => {
  const { addSiteError } = useSiteAlert();

  const columnMap = useMemo(() => {
    const columns = utr?.valueValidation?.table?.columns ?? [];
    return new Map(columns.map((c) => [c.code, c]));
  }, [utr?.valueValidation?.table?.columns]);

  const tableData = useMemo(
    () => table.rows.reduce<InputColumn[][]>((acc, row) => (row.isRemoved ? acc : [...acc, row.data]), []),
    [table.rows],
  );

  const [aggregatedFormRow, setAggregatedFormRow] = useState<InputColumn[][]>();
  const [getAggregatedTableData] = useLazyGetAggregatedTableDataQuery();

  const variationColumnCodes = (initiativeUtr?.valueValidation?.table?.columns ?? []).reduce<string[]>((codes, col) => {
    if (col.validation?.variations?.length) {
      codes.push(col.code);
    }
    return codes;
  }, []);

  const populateAggregatedFormRow = useDebouncedCallback(async () => {
    const validFormRow = formRow && formRow.some((col) => col.value !== undefined);
    if (!variationColumnCodes?.length || !utrvId || !validFormRow) {
      setAggregatedFormRow(undefined);
      return;
    }

    const isAdding = editRowId === -1 && formRow.some((col) => col.value);

    const combinedTableData = isAdding
      ? [...tableData, formRow]
      : tableData.map((existingRow, index) => (index === editRowId ? formRow : existingRow));

    const aggregationCodes = utr?.valueValidation?.table?.aggregation?.columns.map(({ code }) => code);

    if (!aggregationCodes?.length || !combinedTableData.length) {
      return;
    }

    try {
      const { data } = await getAggregatedTableData({
        utrvId,
        tableData: convertInputData(combinedTableData, columnMap),
      });

      const filteredRow = data?.find((aggregatedRow) =>
        aggregationCodes.every(
          (code) => aggregatedRow.find((c) => c.code === code)?.value === formRow.find((c) => c.code === code)?.value,
        ),
      );

      setAggregatedFormRow(filteredRow ? [filteredRow] : undefined);
    } catch (e) {
      addSiteError(e);
    }
  }, 500);

  useEffect(() => {
    if (utr && isTableGroupAggregation(utr)) {
      populateAggregatedFormRow();
    }
  }, [formRow, populateAggregatedFormRow, utr]);

  return aggregatedFormRow;
};
